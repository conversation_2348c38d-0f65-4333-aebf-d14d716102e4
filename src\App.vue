<script setup lang="ts">
import But<PERSON> from './components/ui/Button.vue'
import DragComponent from './components/DragComponent.vue'

const handleButtonClick = (variant: string) => {
  alert(`${variant} button clicked!`)
}
</script>

<template>
  <!-- Button Examples -->
  

  <DragComponent />
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
