<template>
  <div
    class="flex flex-col lg:flex-row gap-6 sm:p-1 bg-slate-100 font-sans min-h-screen lg:h-[calc(100vh-0.5rem)]"
  >
    <div class="lg:w-1/3 w-full p-4 bg-white shadow-lg rounded-xl flex flex-col">
      <h2
        class="text-xl font-semibold text-sky-700 mb-4 pb-2 border-b border-slate-200 flex items-center flex-shrink-0"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6 mr-2 text-sky-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          stroke-width="2"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M4 6h16M4 10h16M4 14h16M4 18h16"
          />
        </svg>
        Danh sách lớp bản đồ
      </h2>
      <div class="relative mb-4 flex-shrink-0">
        <input
          v-model="searchTerm"
          placeholder="Tìm lớp bản đồ..."
          class="w-full px-3 py-2 pl-10 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-shadow"
        />
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          stroke-width="2"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
      <draggable
        :list="filteredSourceItems"
        :group="{ name: 'shared', pull: 'clone', put: false }"
        item-key="id"
        :clone="clone"
        @start="handleDragStart"
        @end="handleDragEnd"
        class="space-y-2 pr-1 flex-grow overflow-y-auto"
      >
        <template #item="{ element }">
          <div
            class="p-3 border border-slate-200 rounded-lg bg-sky-50 shadow-sm cursor-grab hover:bg-sky-100 hover:shadow-md transition-all duration-150 ease-in-out group"
          >
            <span class="text-slate-700 group-hover:text-sky-700"
              >{{ element.name }} ({{ element.title }})</span
            >
          </div>
        </template>
        <template #footer v-if="filteredSourceItems.length === 0">
          <p class="text-sm text-slate-500 italic text-center py-4">
            Không tìm thấy lớp bản đồ nào hoặc tất cả đã được sử dụng.
          </p>
        </template>
      </draggable>
    </div>

    <div class="lg:w-2/3 w-full flex flex-col space-y-6">
      <div
        class="flex-shrink-0 flex flex-wrap justify-between items-center gap-3 p-4 bg-white shadow-lg rounded-xl"
      >
        <h2 class="text-xl font-semibold text-slate-800 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 mr-2 text-sky-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
            />
          </svg>
          Danh sách Dataset
        </h2>
        <button
          @click="addDataset"
          class="bg-sky-600 hover:bg-sky-700 text-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-150 ease-in-out text-sm font-medium flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Tạo Dataset mới
        </button>
      </div>

      <div class="flex-grow overflow-y-auto p-2">
        <draggable
          v-model:list="datasets" item-key="id"
          group="datasetsList" class="space-y-6"     
          ghost-class="draggable-ghost"
          chosen-class="sortable-chosen"
          drag-class="draggable-dragging" >
          <template #item="{ element: dataset, index: datasetIndex }">
            <div
              class="border border-slate-200 rounded-xl bg-white shadow-lg transition-all duration-300 ease-in-out cursor-grab" :class="{ 'ring-2 ring-amber-400 bg-amber-50': dataset.isLocked }"
            >
              <div class="p-4 pb-3 border-b border-slate-200">
                <div class="flex justify-between items-center gap-3">
                  <div class="flex items-center gap-3 flex-grow">
                     <button
                      @click="toggleCollapse(datasetIndex)"
                      class="flex-shrink-0 p-2 rounded-lg hover:bg-slate-100 transition-colors duration-150 text-slate-600 hover:text-sky-600"
                      :title="dataset.isCollapsed ? 'Mở rộng' : 'Thu gọn'"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 transition-transform duration-200"
                        :class="{ 'rotate-180': dataset.isCollapsed }"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        stroke-width="2"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>

                    <input
                      v-model="dataset.datasetName"
                      :disabled="dataset.isLocked"
                      class="text-lg font-medium text-slate-700 border-none focus:outline-none focus:ring-1 focus:ring-sky-500 flex-grow px-2 py-1 rounded-md bg-transparent placeholder-slate-400 transition-colors"
                      :class="{
                        'cursor-not-allowed bg-slate-50 text-slate-500': dataset.isLocked,
                        'hover:bg-slate-50': !dataset.isLocked,
                      }"
                      placeholder="Tên Dataset"
                    />
                  </div>

                  <div class="flex items-center gap-2">
                    <button
                      @click="toggleLock(datasetIndex)"
                      class="p-2 rounded-lg transition-all duration-150 flex-shrink-0"
                      :class="
                        dataset.isLocked
                          ? 'bg-amber-100 text-amber-700 hover:bg-amber-200'
                          : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
                      "
                      :title="dataset.isLocked ? 'Mở khóa chỉnh sửa' : 'Khóa chỉnh sửa'"
                    >
                      <svg
                        v-if="dataset.isLocked"
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        stroke-width="2"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                        />
                      </svg>
                      <svg
                        v-else
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        stroke-width="2"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"
                        />
                      </svg>
                    </button>

                    <button
                      @click="removeDataset(datasetIndex)"
                      :disabled="dataset.isLocked"
                      class="p-2 rounded-lg transition-all duration-150 flex-shrink-0"
                      :class="
                        dataset.isLocked
                          ? 'text-slate-300 cursor-not-allowed'
                          : 'text-slate-400 hover:text-red-600 hover:bg-red-100'
                      "
                      :title="dataset.isLocked ? 'Dataset đã bị khóa' : 'Xóa Dataset'"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        stroke-width="2"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  </div>
                </div>

                <div class="flex items-center gap-4 mt-3 text-sm">
                  <div class="flex items-center gap-2 text-slate-500">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V2a1 1 0 011-1h2a1 1 0 011 1v2m0 0v12a2 2 0 01-2 2H5a2 2 0 01-2-2V4h14z"
                      />
                    </svg>
                    <span>{{ dataset.featureClasses.length }} lớp</span>
                  </div>
                  <div
                    v-if="dataset.isLocked"
                    class="flex items-center gap-1 text-amber-600 bg-amber-100 px-2 py-1 rounded-full"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-3 w-3"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                    <span class="text-xs font-medium">Đã khóa</span>
                  </div>
                </div>
              </div>

              <div v-if="!dataset.isCollapsed" class="p-4 space-y-3">
                <draggable
                  v-model="dataset.featureClasses"
                  :group="{ name: 'shared', pull: true, put: !dataset.isLocked }"
                  :disabled="dataset.isLocked"
                  item-key="id"
                  class="space-y-3 min-h-[80px] bg-slate-50 p-3 rounded-lg border border-dashed transition-colors duration-200"
                  :class="{
                    'border-slate-300 hover:border-slate-400': !dataset.isLocked,
                    'border-slate-200 bg-slate-100': dataset.isLocked,
                  }"
                  @add="(event) => handleFeatureClassAdd(event, datasetIndex)"
                >
                  <template #item="{ element, index }">
                    <div
                      class="flex flex-col gap-2 p-3 bg-white border border-slate-300 rounded-lg shadow-sm hover:shadow-md transition-all duration-150 ease-in-out"
                      :class="{ 'opacity-75': dataset.isLocked }"
                    >
                      <div class="flex items-center">
                        <input
                          v-model="element.name"
                          :disabled="dataset.isLocked"
                          class="flex-grow border-b border-slate-200 bg-transparent focus:outline-none focus:border-sky-500 px-1 text-slate-800 text-sm transition-colors"
                          :class="{
                            'cursor-not-allowed text-slate-500': dataset.isLocked,
                            'hover:border-slate-300': !dataset.isLocked,
                          }"
                          placeholder="Tên Feature Class"
                        />
                        <button
                          @click="removeFeatureClass(datasetIndex, index)"
                          :disabled="dataset.isLocked"
                          class="ml-2 p-1 rounded-full transition-all duration-150 ease-in-out"
                          :class="
                            dataset.isLocked
                              ? 'text-slate-300 cursor-not-allowed'
                              : 'text-slate-400 hover:text-red-500 hover:bg-red-100'
                          "
                          :title="dataset.isLocked ? 'Dataset đã bị khóa' : 'Xóa Feature Class'"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-4 w-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            stroke-width="2"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                      <div class="flex items-center text-xs text-slate-600">
                        <label class="mr-2 font-medium text-slate-500">Z-index:</label>
                        <input
                          type="number"
                          v-model.number="element.zIndex"
                          :disabled="dataset.isLocked"
                          class="w-20 px-2 py-1 border border-slate-300 rounded-md focus:outline-none focus:ring-1 focus:ring-sky-500 focus:border-transparent text-sm transition-colors"
                          :class="{
                            'cursor-not-allowed bg-slate-100 text-slate-500': dataset.isLocked,
                            'hover:border-slate-400': !dataset.isLocked,
                          }"
                        />
                      </div>
                      <div class="text-xs text-slate-400 mt-1">
                        ID gốc: {{ element.originalId }}, Tên gốc: {{ element.originalName }}
                      </div>
                    </div>
                  </template>
                  <template #footer v-if="dataset.featureClasses.length === 0">
                    <div class="text-center py-4">
                      <p class="text-sm text-slate-500 italic">
                        {{
                          dataset.isLocked
                            ? 'Dataset đã bị khóa'
                            : 'Kéo thả lớp bản đồ từ bên trái vào đây.'
                        }}
                      </p>
                    </div>
                  </template>
                </draggable>
              </div>

              <div v-else class="px-4 pb-3">
                <div class="text-sm text-slate-500 italic">
                  Dataset đã thu gọn • {{ dataset.featureClasses.length }} lớp bản đồ
                </div>
              </div>
            </div>
          </template>

          <template #footer>
              <div
                v-if="datasets.length === 0"
                class="text-center py-10 bg-white p-6 rounded-xl shadow-lg"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-12 w-12 mx-auto text-slate-400 mb-3"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  stroke-width="1"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                  />
                </svg>
                <p class="text-slate-500">Chưa có Dataset nào.</p>
                <p class="text-sm text-slate-400">
                  Hãy nhấn "Tạo Dataset mới" để bắt đầu hoặc tải dữ liệu từ DB nếu có.
                </p>
              </div>
          </template>
        </draggable>
      </div>

      <div
        class="flex-shrink-0 bg-white p-4 rounded-xl border border-slate-200 shadow-lg"
        v-if="datasets.length > 0"
      >
        <h3 class="text-md font-semibold text-slate-700 mb-2 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-sky-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
            />
          </svg>
          Preview JSON cấu hình
        </h3>
        <pre
          class="bg-slate-800 text-slate-200 p-3 text-xs overflow-x-auto max-h-64 rounded-md shadow-inner"
          >{{ jsonPreview }}</pre
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Phần script không thay đổi, chỉ có template được cập nhật
import { ref, computed, onBeforeMount } from 'vue'
import draggable from 'vuedraggable'
import { useRoute } from 'vue-router'
// Giả định bạn đã có các import service và type như trong code gốc
// import { wmsService } from '../../services/wms.service'
// import { projectService } from '../../services/project.service'
// import type { WmsLayer, Dataset, FeatureClass } from '../../types/wms.types'

// Mock services and types (GIỮ NGUYÊN MOCK TỪ LẦN TRƯỚC CHO DỄ TEST)
const wmsService = {
  getCapabilities: async (url: string, serverType: string): Promise<any[]> => {
    console.log(`Mock: Fetching WMS capabilities for ${url} (${serverType})`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return [
      { name: 'Layer:Roads', title: 'Giao thông đường bộ' },
      { name: 'Layer:Rivers', title: 'Hệ thống sông ngòi' },
      { name: 'Layer:Buildings', title: 'Các công trình xây dựng' },
      { name: 'Layer:Administrative', title: 'Ranh giới hành chính' },
      { name: 'Layer:Parcels', title: 'Thửa đất chi tiết' },
      { name: 'Layer:Contours', title: 'Đường đồng mức' },
      { name: 'Layer:Vegetation', title: 'Thảm thực vật' },
      { name: 'Layer:Airports', title: 'Sân bay' },
      { name: 'Layer:Railways', title: 'Đường sắt' },
      { name: 'Layer:PointsOfInterest', title: 'Điểm quan tâm (POI)' },
    ].map((l, i) => ({ ...l, id: i, originalLayerData: {...l, id: `wms-original-${i}`} } )); // id là index cho originalId
  }
};
const projectService = {
  getProject: async (projectId: number) => {
    console.log(`Mock: Fetching project ${projectId}`);
    await new Promise(resolve => setTimeout(resolve, 300));
    return {
      success: true,
      data: {
        url_service: 'https://example.com/wms',
        type_service: 'geoserver',
        config_json: JSON.stringify([
          {
            id: 'ds-initial-1',
            datasetName: 'Dataset Hiện trạng sử dụng đất',
            featureClasses: [
              { originalId: 0, originalName: 'Layer:Roads', id: 'fc-initial-roads', name: 'Đường giao thông', zIndex: 1, originalLayerData: { name: 'Layer:Roads', title: 'Giao thông đường bộ' } },
              { originalId: 2, originalName: 'Layer:Buildings', id: 'fc-initial-buildings', name: 'Công trình', zIndex: 2, originalLayerData: { name: 'Layer:Buildings', title: 'Các công trình xây dựng' } },
            ],
            isCollapsed: false,
            isLocked: true, // Test locked dataset
          },
          {
            id: 'ds-initial-2',
            datasetName: 'Dataset Quy hoạch',
            featureClasses: [
              { originalId: 3, originalName: 'Layer:Administrative', id: 'fc-initial-admin', name: 'Ranh giới QH', zIndex: 1, originalLayerData: { name: 'Layer:Administrative', title: 'Ranh giới hành chính' } },
            ],
            isCollapsed: true,
            isLocked: false,
          }
        ]),
      }
    };
  }
};
interface WmsLayer {
  id: string | number;
  name: string;
  title: string;
  originalLayerData?: any;
}
interface FeatureClass {
  id: string;
  name: string;
  originalId: number | string;
  originalName: string;
  zIndex: number;
  originalLayerData?: any;
}
interface Dataset {
  id: string;
  datasetName: string;
  featureClasses: FeatureClass[];
  isCollapsed: boolean;
  isLocked: boolean;
}
// End of mock

const wmsUrl = ref<string>('')
const error = ref<string>('')
const datasets = ref<Dataset[]>([])
const loading = ref<boolean>(false)
let datasetIdCounter = 0
const searchTerm = ref<string>('')
const serverType = ref<'geoserver' | 'qgis'>('geoserver')
const route = useRoute()
const draggedSourceItem = ref<{ item: any; originalIndex: number } | null>(null)
const databaseJson = ref<string | null>(null)

const sourceItems = ref<WmsLayer[]>([])

const loadWmsLayers = async () => {
  if (!wmsUrl.value || !wmsUrl.value.trim()) {
    error.value = 'Không tìm thấy đường dẫn dữ liệu WMS.'
    sourceItems.value = []
    return
  }
  loading.value = true
  error.value = ''
  try {
    const layersFromService: WmsLayer[] = await wmsService.getCapabilities(wmsUrl.value, serverType.value)
    sourceItems.value = layersFromService.map((layer, index) => ({
      id: index, // originalId cho feature class sẽ là index này
      name: layer.name,
      title: layer.title,
      originalLayerData: { ...layer }
    }))
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Lỗi không xác định khi tải WMS layers.'
    sourceItems.value = []
  } finally {
    loading.value = false
  }
}

const loadProjectById = async (projectId: number) => {
  try {
    const response = await projectService.getProject(projectId)
    if (response && response.success && response.data) {
      wmsUrl.value = String(response.data.url_service)
      serverType.value = String(response.data.type_service).toLowerCase() as 'geoserver' | 'qgis'
      await loadWmsLayers()
      const configJsonRaw = response.data.config_json
      if (configJsonRaw) {
        try {
          databaseJson.value = typeof configJsonRaw === 'string' ? configJsonRaw : JSON.stringify(configJsonRaw);
        } catch (e) {
          console.error('❌ Lỗi parse/stringify config_json:', e)
        }
      } else {
        console.warn('⚠️ Không có config_json trong dữ liệu dự án.')
      }
    } else {
      error.value = `Không tải được thông tin dự án ID: ${projectId}.`
    }
  } catch (err) {
    console.error('Error fetching project:', err)
    error.value = `Lỗi khi tải dự án: ${err instanceof Error ? err.message : String(err)}`
  }
}

const filteredSourceItems = computed(() => {
  if (!searchTerm.value.trim()) {
    return sourceItems.value
  }
  return sourceItems.value.filter((item) => {
    const searchTermLower = searchTerm.value.toLowerCase()
    return (
      (item.name && item.name.toLowerCase().includes(searchTermLower)) ||
      (item.title && item.title.toLowerCase().includes(searchTermLower))
    )
  })
})

function addDataset() {
  datasetIdCounter++
  datasets.value.push({
    id: `ds-${Date.now()}-${datasetIdCounter}`,
    datasetName: `Dataset mới ${datasetIdCounter}`,
    featureClasses: [],
    isCollapsed: false,
    isLocked: false,
  })
}

function toggleCollapse(datasetIndex: number) {
  // datasetIndex bây giờ là index trong mảng datasets (có thể đã thay đổi do kéo thả)
  // Vue sẽ phản ứng đúng vì datasets.value[datasetIndex] vẫn trỏ đúng vào đối tượng dataset
  datasets.value[datasetIndex].isCollapsed = !datasets.value[datasetIndex].isCollapsed
}

function toggleLock(datasetIndex: number) {
  datasets.value[datasetIndex].isLocked = !datasets.value[datasetIndex].isLocked
}

function clone(sourceItem: WmsLayer): FeatureClass {
  return {
    originalId: sourceItem.id, // Là index từ sourceItems
    originalName: sourceItem.name,
    originalLayerData: { ...sourceItem.originalLayerData },
    id: `fc-${sourceItem.id}-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
    name: sourceItem.title || sourceItem.name,
    zIndex: 0,
  }
}

function handleDragStart(event: any) {
  if (event.from.closest('.lg\\:w-1\\/3')) {
    const draggedElement = filteredSourceItems.value[event.oldIndex]
    if (draggedElement) {
      const originalIndexInSourceItems = sourceItems.value.findIndex(
        (item) => item.id === draggedElement.id,
      )
      if (originalIndexInSourceItems !== -1) {
        draggedSourceItem.value = {
          item: sourceItems.value[originalIndexInSourceItems],
          originalIndex: originalIndexInSourceItems,
        }
      } else {
        console.warn('Dragged element not found in original sourceItems array.')
        draggedSourceItem.value = null
      }
    } else {
      draggedSourceItem.value = null
    }
  }
}

function handleDragEnd(event: any) {
  if (
    draggedSourceItem.value &&
    event.from.closest('.lg\\:w-1\\/3') &&
    event.to.closest('.lg\\:w-2\\/3')
  ) {
    const indexToRemove = draggedSourceItem.value.originalIndex
    if (
      indexToRemove !== -1 &&
      indexToRemove < sourceItems.value.length &&
      sourceItems.value[indexToRemove]?.id === draggedSourceItem.value.item.id // Thêm ? để tránh lỗi nếu sourceItems[indexToRemove] undefined
    ) {
      sourceItems.value.splice(indexToRemove, 1)
    } else {
      const findIndex = sourceItems.value.findIndex(
        (item) => item.id === draggedSourceItem.value?.item.id,
      )
      if (findIndex !== -1) {
        sourceItems.value.splice(findIndex, 1)
      } else {
        console.warn(
          'Could not remove dragged item from source list: item not found or index mismatch.',
        )
      }
    }
  }
  draggedSourceItem.value = null
}

function addItemToSourceList(fcData: FeatureClass) {
  if (typeof fcData.originalId !== 'undefined' && typeof fcData.originalName !== 'undefined') {
    const alreadyExists = sourceItems.value.some((item) => item.id === fcData.originalId)
    if (!alreadyExists) {
      const itemToAdd: WmsLayer = { // Phải khớp với kiểu WmsLayer (hoặc kiểu của sourceItems)
        id: fcData.originalId, // originalId này là index từ sourceItems ban đầu
        name: fcData.originalName,
        title: fcData.originalLayerData?.title || fcData.originalName,
        originalLayerData: fcData.originalLayerData
          ? { ...fcData.originalLayerData }
          : { name: fcData.originalName, title: fcData.originalName },
      }
      sourceItems.value.push(itemToAdd)
    }
  }
}

function sortSourceItems() {
  sourceItems.value.sort((a, b) => {
    if (typeof a.id === 'number' && typeof b.id === 'number') {
      return a.id - b.id
    }
    return String(a.id).localeCompare(String(b.id))
  })
}

function removeDataset(datasetIndex: number) {
  const dataset = datasets.value[datasetIndex]
  if (dataset.isLocked) {
    alert('❗ Không thể xóa Dataset đã bị khóa. Hãy mở khóa trước khi xóa.')
    return
  }
  dataset.featureClasses.forEach((fc) => {
    addItemToSourceList(fc)
  })
  datasets.value.splice(datasetIndex, 1)
  sortSourceItems()
}

function removeFeatureClass(datasetIndex: number, fcIndex: number) {
  const dataset = datasets.value[datasetIndex]
  if (dataset.isLocked) {
    alert('❗ Không thể xóa Feature Class trong Dataset đã bị khóa.')
    return
  }
  const featureClassToRemove = dataset.featureClasses[fcIndex]
  addItemToSourceList(featureClassToRemove)
  dataset.featureClasses.splice(fcIndex, 1)
  sortSourceItems()
}

function handleFeatureClassAdd(event: any, datasetIndex: number) {
  const dataset = datasets.value[datasetIndex]
  if (dataset.isLocked) {
    // Logic hoàn tác nếu cần thiết, hoặc ngăn chặn từ :move prop của draggable
    if (event.newIndex !== undefined && dataset.featureClasses[event.newIndex]) {
      const addedItem = dataset.featureClasses.splice(event.newIndex, 1)[0];
       if (addedItem && typeof addedItem.originalId !== 'undefined') { // Kiểm tra addedItem và originalId
          addItemToSourceList(addedItem as FeatureClass);
          sortSourceItems();
       }
    }
    alert('❗ Không thể thêm lớp vào Dataset đã bị khóa.');
    return;
  }

  const addedItemInArray = dataset.featureClasses[event.newIndex]
  if (!addedItemInArray) {
    console.error('Không tìm thấy mục được thêm vào mảng đích.')
    if (
      event.item &&
      event.item._underlying_vm_ &&
      typeof event.item._underlying_vm_.originalId !== 'undefined' // Kiểm tra originalId
    ) {
      addItemToSourceList(event.item._underlying_vm_ as FeatureClass)
      sortSourceItems()
    }
    return
  }

  const exists = dataset.featureClasses
    .filter((fc, index) => index !== event.newIndex)
    .some((fc) => fc.originalId === addedItemInArray.originalId)

  if (exists) {
    dataset.featureClasses.splice(event.newIndex, 1)
    alert('❗ Lớp này (dựa trên ID gốc) đã tồn tại trong Dataset này!')
    addItemToSourceList(addedItemInArray as FeatureClass)
    sortSourceItems()
  } else {
    const newZIndex = Number(addedItemInArray.zIndex)
    addedItemInArray.zIndex = isNaN(newZIndex) ? dataset.featureClasses.length || 1 : newZIndex // Z-index nên > 0
    // Đảm bảo zIndex ít nhất là 1 nếu có item, hoặc 1 nếu là item đầu tiên.
    if (addedItemInArray.zIndex === 0 ) {
        addedItemInArray.zIndex = dataset.featureClasses.length > 1 ? dataset.featureClasses.length : 1;
    }
  }
}

const jsonPreview = computed(() => {
  const datasetsForPreview = datasets.value.map((ds) => ({
    ...ds,
    featureClasses: ds.featureClasses.map((fc) => {
      const { originalLayerData, ...rest } = fc
      return rest
    }),
  }))
  return JSON.stringify(datasetsForPreview, null, 2)
})

onBeforeMount(async () => {
  const projectIdFromRoute = Number(route.params.id);
  if (isNaN(projectIdFromRoute)) {
      console.error("Invalid project ID in route. Using default/mock project.");
      // Load một project mock nào đó hoặc WMS rỗng để tránh lỗi
      await loadProjectById(1); // Giả sử có project mock với ID 1
      // Hoặc return nếu không muốn tiếp tục
  } else {
    await loadProjectById(projectIdFromRoute);
  }

  const initialJsonData = databaseJson.value
  if (initialJsonData) {
    try {
      const loadedDatasetsFromDb: Dataset[] = JSON.parse(initialJsonData)
      let maxNumericIdSuffix = 0
      loadedDatasetsFromDb.forEach((ds) => {
        ds.isCollapsed = ds.isCollapsed !== undefined ? ds.isCollapsed : false
        ds.isLocked = ds.isLocked !== undefined ? ds.isLocked : false
        ds.featureClasses = ds.featureClasses.map((fc) => {
          const matchingSourceItem = sourceItems.value.find((si) => si.id === fc.originalId)
          return {
            ...fc,
            id: fc.id || `fc-db-${fc.originalId}-${Date.now()}-${Math.random().toString(36).substr(2,5)}`,
            name: fc.name || fc.originalName || matchingSourceItem?.title || 'Unnamed FC',
            zIndex: fc.zIndex || 0,
            originalLayerData: fc.originalLayerData ||
              matchingSourceItem?.originalLayerData || {
                name: fc.originalName || 'Unknown',
                title: fc.originalName || 'Unknown Title',
              },
          }
        })
        const nameMatch = ds.datasetName && ds.datasetName.match(/(\d+)$/)
        if (nameMatch) {
          const num = parseInt(nameMatch[1])
          if (num > maxNumericIdSuffix) {
            maxNumericIdSuffix = num
          }
        }
      })
      datasets.value = loadedDatasetsFromDb
      datasetIdCounter = maxNumericIdSuffix

      const allFeatureClassOriginalIdsInDbDatasets = new Set<number | string>()
      datasets.value.forEach((dataset) => {
        dataset.featureClasses.forEach((fc) => {
          if (typeof fc.originalId === 'number' || typeof fc.originalId === 'string') {
            allFeatureClassOriginalIdsInDbDatasets.add(fc.originalId)
          }
        })
      })
      sourceItems.value = sourceItems.value.filter((sourceItem) => {
        return !allFeatureClassOriginalIdsInDbDatasets.has(sourceItem.id)
      })
      sortSourceItems()
    } catch (e) {
      console.error('Error processing initial JSON data from database:', e)
      error.value = 'Lỗi khi xử lý dữ liệu khởi tạo từ DB.'
      datasets.value = []
    }
  } else {
    console.log('No initial data found in simulated database.')
  }
  console.log('onBeforeMount finished.')
})
</script>

<style scoped>
/* Make scrollbars a bit more subtle if desired */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}
.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9; /* slate-100 */
  border-radius: 10px;
}
.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #94a3b8; /* slate-400 */
  border-radius: 10px;
}
.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #64748b; /* slate-500 */
}

/* Ghost class for vuedraggable */
.draggable-ghost {
  opacity: 0.4;
  background: #e2e8f0; /* slate-200, slightly different from feature class ghost */
  border: 2px dashed #38bdf8; /* sky-400 */
  border-radius: 0.75rem; /* rounded-xl */
}
.sortable-chosen { /* Item is being chosen */
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  /* outline: 2px solid #0ea5e9; sky-500 */
}
.draggable-dragging { /* Item is actively being dragged */
  /* You might want to slightly lift or change appearance of the item actually being moved */
  opacity: 0.9;
}

/* Optional: improve focus states for accessibility and clarity */
input:focus,
button:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px #38bdf8; /* sky-400 or similar */
}
</style>