<script setup lang="ts">
defineProps<{
  variant?: 'primary' | 'secondary' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
}>()

const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>()
</script>

<template>
  <button
    :class="[
      'btn',
      `btn-${variant || 'primary'}`,
      `btn-${size || 'md'}`,
      { 'btn-disabled': disabled }
    ]"
    :disabled="disabled"
    @click="emit('click', $event)"
  >
    <slot />
  </button>
</template>

<style scoped>
.btn {
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

/* Variants */
.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover:not(.btn-disabled) {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #e5e7eb;
  color: #374151;
}

.btn-secondary:hover:not(.btn-disabled) {
  background-color: #d1d5db;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover:not(.btn-disabled) {
  background-color: #b91c1c;
}

/* Sizes */
.btn-sm {
  padding: 4px 8px;
  font-size: 14px;
}

.btn-md {
  padding: 8px 16px;
  font-size: 16px;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 18px;
}

/* Disabled state */
.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>