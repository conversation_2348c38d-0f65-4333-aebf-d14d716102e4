src/
├── assets/            # Static assets
├── components/        # Shared components
│   ├── ui/            # Base UI components
│   └── common/        # Common components
├── composables/       # Reusable composition functions
├── config/            # App configuration
├── layouts/           # Layout components
├── modules/           # Feature modules
│   └── [module]/
│       ├── components/
│       ├── composables/
│       ├── services/
│       ├── stores/
│       └── views/
├── router/            # Vue Router configuration
├── services/          # API services
├── stores/            # Pinia stores
├── types/             # TypeScript type definitions
├── utils/             # Utility functions
└── views/             # Page components